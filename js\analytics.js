// Analytics and Conversion Tracking for SuperMegaSillyFeet
// This file handles all analytics, tracking, and conversion measurement

// Configuration
const ANALYTICS_CONFIG = {
    // Replace with your actual Google Analytics 4 Measurement ID
    GA4_MEASUREMENT_ID: 'G-XXXXXXXXXX',
    
    // Replace with your actual Google Tag Manager ID (optional)
    GTM_ID: 'GTM-XXXXXXX',
    
    // Facebook Pixel ID (optional)
    FACEBOOK_PIXEL_ID: 'XXXXXXXXXXXXXXXXX',
    
    // Custom tracking endpoints
    CUSTOM_TRACKING_ENDPOINT: '/api/track',
    
    // Debug mode (set to false in production)
    DEBUG_MODE: true
};

// Initialize Google Analytics 4
function initializeGA4() {
    if (ANALYTICS_CONFIG.GA4_MEASUREMENT_ID === 'G-XXXXXXXXXX') {
        console.warn('Please replace GA4_MEASUREMENT_ID with your actual Google Analytics 4 Measurement ID');
        return;
    }

    // Load Google Analytics 4
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${ANALYTICS_CONFIG.GA4_MEASUREMENT_ID}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', ANALYTICS_CONFIG.GA4_MEASUREMENT_ID, {
        page_title: document.title,
        page_location: window.location.href
    });

    // Make gtag globally available
    window.gtag = gtag;

    if (ANALYTICS_CONFIG.DEBUG_MODE) {
        console.log('Google Analytics 4 initialized');
    }
}

// Initialize Google Tag Manager (optional)
function initializeGTM() {
    if (ANALYTICS_CONFIG.GTM_ID === 'GTM-XXXXXXX') {
        return; // Skip if not configured
    }

    // GTM script
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer',ANALYTICS_CONFIG.GTM_ID);

    if (ANALYTICS_CONFIG.DEBUG_MODE) {
        console.log('Google Tag Manager initialized');
    }
}

// Initialize Facebook Pixel (optional)
function initializeFacebookPixel() {
    if (ANALYTICS_CONFIG.FACEBOOK_PIXEL_ID === 'XXXXXXXXXXXXXXXXX') {
        return; // Skip if not configured
    }

    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');

    fbq('init', ANALYTICS_CONFIG.FACEBOOK_PIXEL_ID);
    fbq('track', 'PageView');

    if (ANALYTICS_CONFIG.DEBUG_MODE) {
        console.log('Facebook Pixel initialized');
    }
}

// Track page views
function trackPageView(page_title = document.title, page_location = window.location.href) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('config', ANALYTICS_CONFIG.GA4_MEASUREMENT_ID, {
            page_title: page_title,
            page_location: page_location
        });
    }

    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', 'PageView');
    }

    // Custom tracking
    sendCustomEvent('page_view', {
        page_title: page_title,
        page_location: page_location,
        timestamp: new Date().toISOString()
    });

    if (ANALYTICS_CONFIG.DEBUG_MODE) {
        console.log('Page view tracked:', page_title);
    }
}

// Track conversions
function trackConversion(platform, value = 0, currency = 'USD') {
    const conversionData = {
        platform: platform,
        value: value,
        currency: currency,
        timestamp: new Date().toISOString(),
        page_location: window.location.href
    };

    // Google Analytics 4 - Enhanced Ecommerce
    if (typeof gtag !== 'undefined') {
        gtag('event', 'purchase_intent', {
            event_category: 'Premium Access',
            event_label: platform,
            value: value,
            currency: currency,
            custom_parameters: {
                platform: platform,
                funnel_step: 'conversion'
            }
        });
    }

    // Facebook Pixel - Conversion tracking
    if (typeof fbq !== 'undefined') {
        fbq('track', 'InitiateCheckout', {
            value: value,
            currency: currency,
            content_name: `Premium ${platform} Access`,
            content_category: 'Premium Content'
        });
    }

    // Custom tracking
    sendCustomEvent('conversion', conversionData);

    if (ANALYTICS_CONFIG.DEBUG_MODE) {
        console.log('Conversion tracked:', conversionData);
    }
}

// Track user interactions
function trackInteraction(action, element, additional_data = {}) {
    const interactionData = {
        action: action,
        element: element,
        timestamp: new Date().toISOString(),
        page_location: window.location.href,
        ...additional_data
    };

    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', action, {
            event_category: 'User Interaction',
            event_label: element,
            custom_parameters: additional_data
        });
    }

    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('trackCustom', 'UserInteraction', {
            action: action,
            element: element
        });
    }

    // Custom tracking
    sendCustomEvent('interaction', interactionData);

    if (ANALYTICS_CONFIG.DEBUG_MODE) {
        console.log('Interaction tracked:', interactionData);
    }
}

// Track funnel steps
function trackFunnelStep(step, additional_data = {}) {
    const funnelData = {
        step: step,
        timestamp: new Date().toISOString(),
        page_location: window.location.href,
        ...additional_data
    };

    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', 'funnel_step', {
            event_category: 'Funnel Progress',
            event_label: step,
            custom_parameters: funnelData
        });
    }

    // Custom tracking
    sendCustomEvent('funnel_step', funnelData);

    if (ANALYTICS_CONFIG.DEBUG_MODE) {
        console.log('Funnel step tracked:', funnelData);
    }
}

// Send custom events to your backend
function sendCustomEvent(event_type, data) {
    if (!ANALYTICS_CONFIG.CUSTOM_TRACKING_ENDPOINT) {
        return;
    }

    const payload = {
        event_type: event_type,
        data: data,
        user_agent: navigator.userAgent,
        referrer: document.referrer,
        timestamp: new Date().toISOString()
    };

    // Send to your custom analytics endpoint
    fetch(ANALYTICS_CONFIG.CUSTOM_TRACKING_ENDPOINT, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
    }).catch(error => {
        if (ANALYTICS_CONFIG.DEBUG_MODE) {
            console.error('Custom tracking error:', error);
        }
    });
}

// Track scroll depth
function initializeScrollTracking() {
    let maxScroll = 0;
    const milestones = [25, 50, 75, 90, 100];
    const trackedMilestones = new Set();

    function trackScrollDepth() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = Math.round((scrollTop / docHeight) * 100);

        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;
        }

        milestones.forEach(milestone => {
            if (scrollPercent >= milestone && !trackedMilestones.has(milestone)) {
                trackedMilestones.add(milestone);
                trackInteraction('scroll_depth', `${milestone}%`, {
                    scroll_depth: milestone
                });
            }
        });
    }

    window.addEventListener('scroll', throttle(trackScrollDepth, 500));
}

// Track time on page
function initializeTimeTracking() {
    const startTime = Date.now();
    let lastActiveTime = startTime;
    let totalActiveTime = 0;

    function updateActiveTime() {
        const now = Date.now();
        totalActiveTime += now - lastActiveTime;
        lastActiveTime = now;
    }

    // Track user activity
    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
        document.addEventListener(event, updateActiveTime, true);
    });

    // Send time tracking data before page unload
    window.addEventListener('beforeunload', () => {
        updateActiveTime();
        const timeOnPage = Math.round(totalActiveTime / 1000); // Convert to seconds

        trackInteraction('time_on_page', 'page_exit', {
            time_on_page_seconds: timeOnPage,
            total_time_seconds: Math.round((Date.now() - startTime) / 1000)
        });
    });
}

// Utility function to throttle events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Initialize all analytics
function initializeAnalytics() {
    // Initialize tracking platforms
    initializeGA4();
    initializeGTM();
    initializeFacebookPixel();

    // Initialize behavior tracking
    initializeScrollTracking();
    initializeTimeTracking();

    // Track initial page view
    trackPageView();

    // Track funnel entry
    trackFunnelStep('landing_page_view');

    if (ANALYTICS_CONFIG.DEBUG_MODE) {
        console.log('All analytics initialized');
    }
}

// Export functions for use in other scripts
window.Analytics = {
    trackConversion,
    trackInteraction,
    trackFunnelStep,
    trackPageView,
    initializeAnalytics
};

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAnalytics);
} else {
    initializeAnalytics();
}
