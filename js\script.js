// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 1000,
    once: true,
    offset: 100
});

// Mobile Navigation Toggle
const navToggle = document.querySelector('.nav-toggle');
const navMenu = document.querySelector('.nav-menu');

navToggle.addEventListener('click', () => {
    navMenu.classList.toggle('active');
    
    // Animate hamburger menu
    const bars = navToggle.querySelectorAll('.bar');
    bars.forEach((bar, index) => {
        if (navMenu.classList.contains('active')) {
            if (index === 0) bar.style.transform = 'rotate(45deg) translate(5px, 5px)';
            if (index === 1) bar.style.opacity = '0';
            if (index === 2) bar.style.transform = 'rotate(-45deg) translate(7px, -6px)';
        } else {
            bar.style.transform = 'none';
            bar.style.opacity = '1';
        }
    });
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', () => {
        navMenu.classList.remove('active');
        const bars = navToggle.querySelectorAll('.bar');
        bars.forEach(bar => {
            bar.style.transform = 'none';
            bar.style.opacity = '1';
        });
    });
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Navbar background change on scroll
window.addEventListener('scroll', () => {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
        navbar.style.background = 'rgba(0, 0, 0, 0.98)';
    } else {
        navbar.style.background = 'rgba(0, 0, 0, 0.95)';
    }
});

// Gallery item click handlers
document.querySelectorAll('.gallery-item').forEach((item, index) => {
    item.addEventListener('click', () => {
        // Track gallery interaction
        if (window.Analytics && typeof window.Analytics.trackInteraction === 'function') {
            window.Analytics.trackInteraction('gallery_item_click', `gallery_item_${index + 1}`, {
                item_index: index + 1,
                funnel_step: 'gallery_engagement'
            });
        }

        // Track funnel step
        if (window.Analytics && typeof window.Analytics.trackFunnelStep === 'function') {
            window.Analytics.trackFunnelStep('gallery_item_clicked', {
                item_index: index + 1
            });
        }

        // Add click animation
        item.style.transform = 'scale(0.95)';
        setTimeout(() => {
            item.style.transform = 'scale(1)';
        }, 150);

        // Show modal or redirect to premium content
        showPremiumModal();
    });
});

// Premium button click handlers
document.querySelectorAll('.premium-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
        e.preventDefault();
        const platform = btn.getAttribute('data-platform');

        // Track premium button click
        if (window.Analytics && typeof window.Analytics.trackInteraction === 'function') {
            window.Analytics.trackInteraction('premium_button_click', `premium_${platform}`, {
                platform: platform,
                funnel_step: 'premium_selection'
            });
        }

        // Track funnel step
        if (window.Analytics && typeof window.Analytics.trackFunnelStep === 'function') {
            window.Analytics.trackFunnelStep('premium_button_clicked', {
                platform: platform
            });
        }

        redirectToPlatform(platform);
    });
});

// Hero play button click handler
document.querySelector('.hero-overlay').addEventListener('click', () => {
    // Track hero play button click
    if (window.Analytics && typeof window.Analytics.trackInteraction === 'function') {
        window.Analytics.trackInteraction('hero_play_click', 'hero_video_button', {
            funnel_step: 'hero_engagement'
        });
    }

    // Track funnel step
    if (window.Analytics && typeof window.Analytics.trackFunnelStep === 'function') {
        window.Analytics.trackFunnelStep('hero_video_clicked');
    }

    showVideoModal();
});

// Show premium access modal
function showPremiumModal() {
    // Create modal overlay
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Premium Content Access</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>To view this exclusive content, please choose your preferred platform:</p>
                <div class="modal-buttons">
                    <button class="btn btn-primary" onclick="redirectToPlatform('photos')">
                        <i class="fas fa-images"></i> Photo Platform
                    </button>
                    <button class="btn btn-primary" onclick="redirectToPlatform('videos')">
                        <i class="fas fa-video"></i> Video Platform
                    </button>
                    <button class="btn btn-primary" onclick="redirectToPlatform('vip')">
                        <i class="fas fa-crown"></i> VIP Access
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add modal styles
    const modalStyles = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }
        
        .modal-content {
            background: white;
            border-radius: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .modal-header {
            padding: 2rem 2rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            color: #999;
        }
        
        .modal-body {
            padding: 2rem;
        }
        
        .modal-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .modal-buttons .btn {
            justify-content: center;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    `;
    
    // Add styles to head if not already added
    if (!document.querySelector('#modal-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'modal-styles';
        styleSheet.textContent = modalStyles;
        document.head.appendChild(styleSheet);
    }
    
    // Close modal handlers
    modal.querySelector('.modal-close').addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

// Show video modal
function showVideoModal() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Premium Video Preview</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="video-preview">
                    <div class="video-placeholder">
                        <i class="fas fa-play-circle" style="font-size: 4rem; color: #ff6b6b;"></i>
                        <p>Premium video content available with subscription</p>
                    </div>
                </div>
                <p style="margin-top: 1rem;">Get access to exclusive HD videos and photo collections.</p>
                <button class="btn btn-primary" style="width: 100%; margin-top: 1rem;" onclick="redirectToPlatform('videos')">
                    Get Video Access
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal handlers
    modal.querySelector('.modal-close').addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

// Redirect to external platforms
function redirectToPlatform(platform) {
    // Track conversion
    trackConversion(platform);
    
    // Platform URLs - Replace these with actual platform URLs
    const platformUrls = {
        photos: 'https://example-photo-platform.com/supermegasillyfeet',
        videos: 'https://example-video-platform.com/supermegasillyfeet',
        vip: 'https://example-vip-platform.com/supermegasillyfeet'
    };
    
    // Show loading state
    const loadingModal = document.createElement('div');
    loadingModal.className = 'modal-overlay';
    loadingModal.innerHTML = `
        <div class="modal-content" style="text-align: center; padding: 3rem;">
            <div class="loading-spinner"></div>
            <h3>Redirecting to ${platform.toUpperCase()} platform...</h3>
            <p>You will be redirected in a moment.</p>
        </div>
    `;
    
    // Add loading spinner styles
    const spinnerStyles = `
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #ff6b6b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 2rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    
    if (!document.querySelector('#spinner-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'spinner-styles';
        styleSheet.textContent = spinnerStyles;
        document.head.appendChild(styleSheet);
    }
    
    document.body.appendChild(loadingModal);
    
    // Simulate redirect delay and then open URL
    setTimeout(() => {
        // Remove any existing modals
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            document.body.removeChild(modal);
        });
        
        // Open platform URL in new tab
        window.open(platformUrls[platform] || '#', '_blank');
    }, 2000);
}

// Track conversions for analytics
function trackConversion(platform) {
    // Use the Analytics module if available
    if (window.Analytics && typeof window.Analytics.trackConversion === 'function') {
        // Determine value based on platform
        const platformValues = {
            photos: 19.99,
            videos: 29.99,
            vip: 49.99
        };

        window.Analytics.trackConversion(platform, platformValues[platform] || 0);
    }

    // Fallback to direct gtag if Analytics module not available
    if (typeof gtag !== 'undefined') {
        gtag('event', 'conversion', {
            'event_category': 'Premium Access',
            'event_label': platform,
            'value': 1
        });
    }

    // Console log for development
    console.log(`Conversion tracked: ${platform} platform access`);
}

// Parallax effect for hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const hero = document.querySelector('.hero');
    if (hero) {
        hero.style.transform = `translateY(${scrolled * 0.5}px)`;
    }
});

// Add loading animation to buttons
document.querySelectorAll('.btn').forEach(btn => {
    btn.addEventListener('click', function() {
        if (!this.classList.contains('loading')) {
            this.classList.add('loading');
            const originalText = this.textContent;
            this.textContent = 'Loading...';
            
            setTimeout(() => {
                this.classList.remove('loading');
                this.textContent = originalText;
            }, 1000);
        }
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate');
        }
    });
}, observerOptions);

// Observe elements for animation
document.querySelectorAll('.feature-card, .gallery-item, .premium-card').forEach(el => {
    observer.observe(el);
});

// Add custom CSS for loading states
const loadingStyles = `
    .btn.loading {
        opacity: 0.7;
        pointer-events: none;
    }
    
    .animate {
        animation: slideInUp 0.6s ease forwards;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .video-placeholder {
        background: #f8f9fa;
        padding: 3rem;
        border-radius: 15px;
        text-align: center;
        border: 2px dashed #ddd;
    }
`;

if (!document.querySelector('#loading-styles')) {
    const styleSheet = document.createElement('style');
    styleSheet.id = 'loading-styles';
    styleSheet.textContent = loadingStyles;
    document.head.appendChild(styleSheet);
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('SuperMegaSillyFeet website loaded successfully!');
    
    // Add any initialization code here
    // For example, loading user preferences, checking authentication, etc.
});
